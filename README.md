# Windows Native Plugin for Unity

这是一个为 Unity 应用程序提供 Windows 原生功能的 DLL 插件，支持窗口管理、剪贴板操作、任务栏控制等功能。

## 功能特性

- **窗口管理**：全屏、最大化、最小化、无边框、圆角窗口等
- **剪贴板操作**：文本和图像的读写
- **任务栏控制**：进度条、闪烁提醒等
- **系统信息**：用户名、机器名、设备标识等
- **拖放支持**：文件拖放到应用程序
- **鼠标控制**：获取和设置鼠标位置

## 系统要求

### 支持的操作系统
- Windows 10 (版本 1903 及以上)
- Windows 11 (所有版本)

### 运行时依赖
- Microsoft Visual C++ 2022 Redistributable (x64) - 如果使用动态链接版本
- 当前版本使用静态链接，无需额外的运行时库

## 兼容性说明

### Windows 10 兼容性
本插件已针对 Windows 10 兼容性进行优化：

1. **静态链接运行时库**：避免了对特定版本 Visual C++ Redistributable 的依赖
2. **动态 API 检测**：在运行时检测 API 可用性，确保在不同 Windows 版本中的兼容性
3. **降级处理**：对于 Windows 11 特有的功能（如原生圆角窗口），在 Windows 10 中使用兼容的替代方案

### 已知问题和解决方案

#### 问题：在 Windows 10 中出现 "Fallback handler could not load library" 错误

**可能原因：**
1. 缺少必要的系统更新
2. 系统缺少某些 API 支持
3. 权限问题

**解决方案：**
1. 确保 Windows 10 已更新到最新版本
2. 以管理员权限运行应用程序
3. 检查 Windows Defender 或其他安全软件是否阻止了 DLL 加载
4. 确保 DLL 文件没有被损坏

## 编译说明

### 开发环境
- Visual Studio 2022
- Windows 10 SDK (10.0.19041.0 或更高)
- C++17 标准

### 编译配置
- **平台**：x64
- **配置**：Release
- **运行时库**：静态链接 (/MT)
- **字符集**：Unicode

### 编译步骤
1. 打开 `WindowsNativePlugin.sln`
2. 选择 Release | x64 配置
3. 生成解决方案
4. 生成的 DLL 文件位于 `x64/Release/WindowsNativePlugin.dll`

## 使用方法

1. 将编译生成的 `WindowsNativePlugin.dll` 复制到 Unity 项目的 `Assets/Plugins` 目录
2. 在 Unity 中配置 DLL 的平台设置为 Windows x64
3. 在 C# 脚本中使用 `[DllImport]` 调用相应的函数

### 示例代码
```csharp
using System.Runtime.InteropServices;

public class WindowsNativePlugin
{
    [DllImport("WindowsNativePlugin")]
    public static extern void SetWindowFullscreen();
    
    [DllImport("WindowsNativePlugin")]
    public static extern void SetWindowStyleRoundedFrameless(int cornerRadius);
}
```

## 故障排除

### DLL 加载失败
1. 检查 DLL 文件是否存在于正确的路径
2. 确认目标系统架构匹配（x64）
3. 检查 Windows 版本兼容性
4. 查看 Unity Console 中的详细错误信息

### 功能异常
1. 某些功能可能在不同 Windows 版本中表现不同
2. 圆角窗口功能在 Windows 10 中使用备用实现
3. DPI 感知功能需要系统支持

## 版本历史

### v1.1.0
- 改进 Windows 10 兼容性
- 添加动态 API 检测
- 使用静态链接运行时库
- 优化错误处理

### v1.0.0
- 初始版本
- 基本窗口管理功能
- 剪贴板操作
- 任务栏控制

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 技术支持

如果在使用过程中遇到问题，请提供以下信息：
- Windows 版本和构建号
- Unity 版本
- 错误信息的完整内容
- 复现步骤
