# Windows Native Plugin for Unity

这是一个为 Unity 应用程序提供 Windows 原生功能的 DLL 插件，支持窗口管理、剪贴板操作、任务栏控制等功能。

## 功能特性

- **窗口管理**：全屏、最大化、最小化、无边框、圆角窗口等
- **剪贴板操作**：文本和图像的读写
- **任务栏控制**：进度条、闪烁提醒等
- **系统信息**：用户名、机器名、设备标识等
- **拖放支持**：文件拖放到应用程序
- **鼠标控制**：获取和设置鼠标位置

## 系统要求

### 支持的操作系统
- Windows XP SP3 (32位和64位)
- Windows Vista (所有版本)
- Windows 7 (所有版本)
- Windows 8/8.1 (所有版本)
- Windows 10 (所有版本)
- Windows 11 (所有版本)

### 运行时依赖
- 当前版本使用静态链接，无需额外的运行时库
- Windows XP 需要 Service Pack 3
- 某些高级功能在较老系统中可能不可用或有限制

## 兼容性说明

### 跨版本兼容性
本插件已针对广泛的 Windows 版本兼容性进行优化：

1. **静态链接运行时库**：避免了对特定版本 Visual C++ Redistributable 的依赖
2. **动态 API 检测**：在运行时检测 API 可用性，确保在不同 Windows 版本中的兼容性
3. **降级处理**：对于较新系统的功能，在老系统中使用兼容的替代方案或禁用功能
4. **版本检测**：自动检测操作系统版本，只启用支持的功能

### 功能兼容性表

| 功能 | Windows XP | Windows Vista | Windows 7 | Windows 8+ |
|------|------------|---------------|-----------|------------|
| 基本窗口管理 | ✅ | ✅ | ✅ | ✅ |
| 窗口透明度 | ✅ | ✅ | ✅ | ✅ |
| 圆角窗口 | ✅ (区域方式) | ✅ (DWM+区域) | ✅ (DWM+区域) | ✅ (原生支持) |
| 任务栏进度条 | ❌ | ❌ | ✅ | ✅ |
| DPI 感知 | ❌ | ❌ | ❌ | ✅ |
| 剪贴板操作 | ✅ | ✅ | ✅ | ✅ |
| 拖放支持 | ✅ | ✅ | ✅ | ✅ |

### 已知问题和解决方案

#### 问题：在较老系统中出现 "Fallback handler could not load library" 错误

**可能原因：**
1. 系统缺少必要的更新（特别是 Windows XP SP3）
2. 使用了不兼容的编译配置
3. 权限问题
4. 缺少必要的系统组件

**解决方案：**
1. **Windows XP**：确保已安装 Service Pack 3
2. **所有系统**：以管理员权限运行应用程序
3. 检查防病毒软件是否阻止了 DLL 加载
4. 确保使用正确的架构版本（32位系统使用32位DLL）
5. 检查系统是否安装了必要的 Windows 组件

#### Windows XP 特别注意事项
- 某些现代功能（如任务栏进度条、DPI感知）在 XP 中不可用
- 圆角窗口使用传统的区域方式实现，可能性能较低
- 建议在 XP 中禁用高级视觉效果以获得最佳性能

## 编译说明

### 开发环境
- Visual Studio 2022
- Windows 10 SDK (10.0.19041.0 或更高)
- C++17 标准

### 编译配置
- **平台**：Win32 (使用 v143_xp 工具集) 或 x64
- **配置**：Release
- **运行时库**：静态链接 (/MT)
- **字符集**：Unicode
- **Windows 版本**：
  - Win32: Windows XP (0x0501)
  - x64: Windows 7 (0x0601)

### 编译步骤
1. 打开 `WindowsNativePlugin.sln`
2. 选择目标配置：
   - **Windows XP 32位**：Release | Win32
   - **Windows 7+ 64位**：Release | x64
3. 生成解决方案
4. 生成的 DLL 文件位于：
   - 32位：`Release/WindowsNativePlugin.dll`
   - 64位：`x64/Release/WindowsNativePlugin.dll`

## 使用方法

1. 将编译生成的 `WindowsNativePlugin.dll` 复制到 Unity 项目的 `Assets/Plugins` 目录
2. 在 Unity 中配置 DLL 的平台设置为 Windows x64
3. 在 C# 脚本中使用 `[DllImport]` 调用相应的函数

### 示例代码
```csharp
using System.Runtime.InteropServices;

public class WindowsNativePlugin
{
    [DllImport("WindowsNativePlugin")]
    public static extern void SetWindowFullscreen();
    
    [DllImport("WindowsNativePlugin")]
    public static extern void SetWindowStyleRoundedFrameless(int cornerRadius);
}
```

## 故障排除

### DLL 加载失败
1. 检查 DLL 文件是否存在于正确的路径
2. 确认目标系统架构匹配（x64）
3. 检查 Windows 版本兼容性
4. 查看 Unity Console 中的详细错误信息

### 功能异常
1. 某些功能可能在不同 Windows 版本中表现不同
2. 圆角窗口功能在 Windows 10 中使用备用实现
3. DPI 感知功能需要系统支持

## 版本历史

### v1.2.0
- 添加 Windows XP 和 Windows 7 兼容性
- 实现全面的操作系统版本检测
- 为不支持的功能提供降级处理
- 优化老系统上的性能表现

### v1.1.0
- 改进 Windows 10 兼容性
- 添加动态 API 检测
- 使用静态链接运行时库
- 优化错误处理

### v1.0.0
- 初始版本
- 基本窗口管理功能
- 剪贴板操作
- 任务栏控制

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 技术支持

如果在使用过程中遇到问题，请提供以下信息：
- Windows 版本和构建号
- Unity 版本
- 错误信息的完整内容
- 复现步骤
