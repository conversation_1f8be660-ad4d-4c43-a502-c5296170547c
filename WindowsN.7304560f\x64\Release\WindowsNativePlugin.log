﻿  WindowsNativePlugin.cpp
D:\vsProject\WindowsNativePlugin\WindowsNativePlugin.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\vsProject\WindowsNativePlugin\WindowsNativePlugin.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“WindowsNativePlugin.cpp”)
  
D:\vsProject\WindowsNativePlugin\WindowsNativePlugin.cpp(35,13): error C4996: 'GetVersionExW': 被声明为已否决
D:\vsProject\WindowsNativePlugin\WindowsNativePlugin.cpp(948,52): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\vsProject\WindowsNativePlugin\WindowsNativePlugin.cpp(975,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: “=”: 从“wchar_t”转换到“char”，可能丢失数据
  (编译源文件“WindowsNativePlugin.cpp”)
      D:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      模板实例化上下文(最早的实例化上下文)为
          D:\vsProject\WindowsNativePlugin\WindowsNativePlugin.cpp(943,40):
          查看对正在编译的函数 模板 实例化“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)”的引用
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              D:\vsProject\WindowsNativePlugin\WindowsNativePlugin.cpp(943,40):
              请参阅 "DragDropWndProc" 中对 "std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string" 的第一个引用
          D:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          查看对正在编译的函数 模板 实例化“void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)”的引用
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          D:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          查看对正在编译的函数 模板 实例化“_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)”的引用
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
