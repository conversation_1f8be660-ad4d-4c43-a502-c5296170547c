﻿  WindowsNativePlugin.cpp
D:\vsProject\WindowsNativePlugin\WindowsNativePlugin.cpp(850,52): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18): warning C4244: “=”: 从“wchar_t”转换到“char”，可能丢失数据
  (编译源文件“WindowsNativePlugin.cpp”)
      D:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xutility(4829,18):
      模板实例化上下文(最早的实例化上下文)为
          D:\vsProject\WindowsNativePlugin\WindowsNativePlugin.cpp(845,40):
          查看对正在编译的函数 模板 实例化“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(_Iter,_Iter,const _Alloc &)”的引用
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Alloc=std::allocator<char>
          ]
              D:\vsProject\WindowsNativePlugin\WindowsNativePlugin.cpp(845,40):
              请参阅 "DragDropWndProc" 中对 "std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string" 的第一个引用
          D:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(788,17):
          查看对正在编译的函数 模板 实例化“void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)”的引用
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          D:\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.43.34808\include\xstring(944,18):
          查看对正在编译的函数 模板 实例化“_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)”的引用
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
    正在创建库 D:\vsProject\WindowsNativePlugin\x64\Release\WindowsNativePlugin.lib 和对象 D:\vsProject\WindowsNativePlugin\x64\Release\WindowsNativePlugin.exp
  正在生成代码
  Previous IPDB not found, fall back to full compilation.
  All 158 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  已完成代码的生成
  WindowsNativePlugin.vcxproj -> D:\vsProject\WindowsNativePlugin\x64\Release\WindowsNativePlugin.dll
