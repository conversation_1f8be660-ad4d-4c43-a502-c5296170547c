#include "WindowsNativePlugin.h"
#include <Windows.h>
#include <ShlObj.h>
#include <string>
#include <vector>
#include <memory>
#include <ShellScalingApi.h> // �������ͷ�ļ�
#include <dwmapi.h>          // ����DWM APIͷ�ļ�

// ����COM������
#pragma comment(lib, "Shcore.lib")
#pragma comment(lib, "dwmapi.lib") // ����DWM������

// ȫ�ֱ�������Բ�ǰ뾶��״̬
static int g_cornerRadius = 0;
static bool g_isRoundedFrameless = false;

// ��ȡUnity���ھ��
HWND GetUnityWindowHandle()
{
    return GetActiveWindow();
}

// �ַ�����������
char* AllocateString(const std::string& str)
{
    char* result = new char[str.length() + 1];
    strcpy_s(result, str.length() + 1, str.c_str());
    return result;
}

#pragma region Window API

// ����״̬����
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowFullscreen()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        MONITORINFO mi = { sizeof(mi) };
        if (GetMonitorInfo(MonitorFromWindow(hwnd, MONITOR_DEFAULTTOPRIMARY), &mi))
        {
            SetWindowLong(hwnd, GWL_STYLE, WS_POPUP | WS_VISIBLE);
            SetWindowPos(hwnd, HWND_TOP,
                mi.rcMonitor.left, mi.rcMonitor.top,
                mi.rcMonitor.right - mi.rcMonitor.left,
                mi.rcMonitor.bottom - mi.rcMonitor.top,
                SWP_FRAMECHANGED);
        }
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowMaximize()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        ShowWindow(hwnd, SW_MAXIMIZE);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowNormal()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        ShowWindow(hwnd, SW_NORMAL);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowMinimize()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        ShowWindow(hwnd, SW_MINIMIZE);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API RestoreWindow()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        ShowWindow(hwnd, SW_RESTORE);
    }
}

// ������ʽ����
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFramed()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = WS_OVERLAPPEDWINDOW | WS_VISIBLE;
        SetWindowLong(hwnd, GWL_STYLE, style);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFramedWithoutTitleBar()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = WS_OVERLAPPED | WS_VISIBLE | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_SYSMENU;
        SetWindowLong(hwnd, GWL_STYLE, style);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFramedWithoutButtons()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = WS_OVERLAPPED | WS_VISIBLE | WS_BORDER | WS_CAPTION;
        SetWindowLong(hwnd, GWL_STYLE, style);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFrameless()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = WS_POPUP | WS_VISIBLE;
        SetWindowLong(hwnd, GWL_STYLE, style);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);

        // ����Բ��״̬
        g_isRoundedFrameless = false;
    }
}

// ����Բ���ޱ߿򴰿�֧��
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleRoundedFrameless(int cornerRadius)
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd) return;

    // ����Բ�ǰ뾶
    g_cornerRadius = cornerRadius;
    g_isRoundedFrameless = true;

    // �����ޱ߿򴰿�
    LONG style = WS_POPUP | WS_VISIBLE;
    SetWindowLong(hwnd, GWL_STYLE, style);

    // ������չ��ʽ
    LONG exStyle = GetWindowLong(hwnd, GWL_EXSTYLE);
    exStyle |= WS_EX_LAYERED;
    SetWindowLong(hwnd, GWL_EXSTYLE, exStyle);

    // Ӧ��Բ��
    MARGINS margins = { 0, 0, 0, 0 };
    DwmExtendFrameIntoClientArea(hwnd, &margins);

    // 检查是否支持Windows 11的圆角API
    typedef HRESULT(WINAPI* DwmSetWindowAttributeFunc)(HWND, DWORD, LPCVOID, DWORD);
    HMODULE hDwmapi = GetModuleHandle(L"dwmapi.dll");
    if (hDwmapi)
    {
        DwmSetWindowAttributeFunc pDwmSetWindowAttribute =
            (DwmSetWindowAttributeFunc)GetProcAddress(hDwmapi, "DwmSetWindowAttribute");

        if (pDwmSetWindowAttribute)
        {
            // 尝试使用Windows 11的圆角API
            const DWORD DWMWA_WINDOW_CORNER_PREFERENCE = 33;
            const DWORD DWMWCP_ROUND = 2;
            DWORD preference = DWMWCP_ROUND;

            HRESULT hr = pDwmSetWindowAttribute(hwnd, DWMWA_WINDOW_CORNER_PREFERENCE, &preference, sizeof(preference));
            if (SUCCEEDED(hr))
            {
                // Windows 11圆角API成功，直接返回
                SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
                return;
            }
        }
    }

    // 对于Windows 10或API不可用的情况，使用备用方法
    RECT rect;
    GetWindowRect(hwnd, &rect);
    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;

    // 创建圆角区域
    HRGN region = CreateRoundRectRgn(0, 0, width + 1, height + 1, cornerRadius, cornerRadius);
    SetWindowRgn(hwnd, region, TRUE);
    DeleteObject(region);

    // ˢ�´���
    SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
}

extern "C" UNITY_INTERFACE_EXPORT int UNITY_INTERFACE_API GetWindowCornerRadius()
{
    return g_cornerRadius;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleRoundedFrameless()
{
    return g_isRoundedFrameless;
}

// ������ʽ��ѯ
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFramed()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_OVERLAPPEDWINDOW) == WS_OVERLAPPEDWINDOW;
    }
    return false;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFramedWithoutTitleBar()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_CAPTION) == 0 && (style & WS_OVERLAPPED) != 0;
    }
    return false;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFramedWithoutButtons()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_CAPTION) != 0 && (style & WS_MINIMIZEBOX) == 0 && (style & WS_MAXIMIZEBOX) == 0;
    }
    return false;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFrameless()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_POPUP) != 0 && (style & WS_OVERLAPPEDWINDOW) == 0;
    }
    return false;
}

// ���ڰ�ť����
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableCloseButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        HMENU hMenu = GetSystemMenu(hwnd, FALSE);
        EnableMenuItem(hMenu, SC_CLOSE, MF_ENABLED);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableCloseButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        HMENU hMenu = GetSystemMenu(hwnd, FALSE);
        EnableMenuItem(hMenu, SC_CLOSE, MF_GRAYED);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableMaximizeButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        SetWindowLong(hwnd, GWL_STYLE, style | WS_MAXIMIZEBOX);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableMaximizeButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        SetWindowLong(hwnd, GWL_STYLE, style & ~WS_MAXIMIZEBOX);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableMinimizeButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        SetWindowLong(hwnd, GWL_STYLE, style | WS_MINIMIZEBOX);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableMinimizeButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        SetWindowLong(hwnd, GWL_STYLE, style & ~WS_MINIMIZEBOX);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

// ���ڰ�ť״̬��ѯ
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsCloseButtonEnabled()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        HMENU hMenu = GetSystemMenu(hwnd, FALSE);
        UINT state = GetMenuState(hMenu, SC_CLOSE, MF_BYCOMMAND);
        return (state & MF_GRAYED) == 0;
    }
    return false;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsMaximizeButtonEnabled()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_MAXIMIZEBOX) != 0;
    }
    return false;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsMinimizeButtonEnabled()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_MINIMIZEBOX) != 0;
    }
    return false;
}

// ����λ�úʹ�С
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowPosition(int x, int y)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        SetWindowPos(hwnd, NULL, x, y, 0, 0, SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowSize(int width, int height)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        SetWindowPos(hwnd, NULL, 0, 0, width, height, SWP_NOMOVE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetWindowPosition(int* x, int* y)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        RECT rect;
        GetWindowRect(hwnd, &rect);
        *x = rect.left;
        *y = rect.top;
    }
    else
    {
        *x = 0;
        *y = 0;
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetWindowSize(int* width, int* height)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        RECT rect;
        GetWindowRect(hwnd, &rect);
        *width = rect.right - rect.left;
        *height = rect.bottom - rect.top;
    }
    else
    {
        *width = 0;
        *height = 0;
    }
}
// ���Ӵ����϶�֧��
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API StartWindowDrag()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        // ʹ��Windowsԭ�����϶���Ϣ
        ReleaseCapture();
        SendMessage(hwnd, WM_NCLBUTTONDOWN, HTCAPTION, 0);

        // ȷ����겶���ͷ�
        SetCapture(NULL);
    }
}
// ���ڱ���
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowTitle(const char* title)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        SetWindowTextA(hwnd, title);
    }
}

extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetWindowTitle()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        int length = GetWindowTextLengthA(hwnd);
        if (length > 0)
        {
            std::string title(length + 1, '\0');
            GetWindowTextA(hwnd, &title[0], length + 1);
            return AllocateString(title);
        }
    }
    return AllocateString(std::string("")); // �޸���ʹ��std::string��װ���ַ���
}

// ����͸����
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowAlpha(byte alpha)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG_PTR exStyle = GetWindowLongPtr(hwnd, GWL_EXSTYLE);
        if (!(exStyle & WS_EX_LAYERED))
        {
            SetWindowLongPtr(hwnd, GWL_EXSTYLE, exStyle | WS_EX_LAYERED);
        }
        SetLayeredWindowAttributes(hwnd, 0, alpha, LWA_ALPHA);
    }
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetWindowHasAlpha()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG_PTR exStyle = GetWindowLongPtr(hwnd, GWL_EXSTYLE);
        return (exStyle & WS_EX_LAYERED) != 0;
    }
    return false;
}

#pragma endregion

#pragma region Clipboard API

// �������ı�����
// �޸� SetClipboardText �����е� size_t �� int ת������
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetClipboardText(const char* text, bool emptyClipboardBeforeSet)
{
    if (!OpenClipboard(NULL))
        return;

    if (emptyClipboardBeforeSet)
        EmptyClipboard();

    size_t len = strlen(text) + 1;
    // ʹ�� SIZE_T ���ͻ���ʽת�������⾯��
    HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, static_cast<SIZE_T>(len));
    if (hMem)
    {
        char* pMem = static_cast<char*>(GlobalLock(hMem));
        if (pMem)
        {
            memcpy(pMem, text, len);
            GlobalUnlock(hMem);
            SetClipboardData(CF_TEXT, hMem);
        }
    }

    CloseClipboard();
}

extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetClipboardText()
{
    if (!OpenClipboard(NULL))
        return AllocateString(std::string("")); // �޸���ʹ��std::string��װ���ַ���

    HANDLE hData = GetClipboardData(CF_TEXT);
    if (!hData)
    {
        CloseClipboard();
        return AllocateString(std::string("")); // �޸���ʹ��std::string��װ���ַ���
    }

    char* pText = (char*)GlobalLock(hData);
    if (!pText)
    {
        CloseClipboard();
        return AllocateString(std::string("")); // �޸���ʹ��std::string��װ���ַ���
    }

    const char* result = AllocateString(std::string(pText)); // �޸���ʹ��std::string��װ
    GlobalUnlock(hData);
    CloseClipboard();
    return result;
}

// ������ͼ�����
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetClipboardImage(void* texturePtr, int width, int height, bool emptyClipboardBeforeSet)
{
    if (!OpenClipboard(NULL))
        return;

    if (emptyClipboardBeforeSet)
        EmptyClipboard();

    // ����DIBλͼ
    BITMAPINFOHEADER bi = { 0 };
    bi.biSize = sizeof(BITMAPINFOHEADER);
    bi.biWidth = width;
    bi.biHeight = -height; // ��ֵ��ʾ���϶��µ�λͼ
    bi.biPlanes = 1;
    bi.biBitCount = 32;
    bi.biCompression = BI_RGB;

    HDC hdc = GetDC(NULL);
    HBITMAP hBitmap = CreateDIBitmap(hdc, &bi, CBM_INIT, texturePtr, (BITMAPINFO*)&bi, DIB_RGB_COLORS);
    ReleaseDC(NULL, hdc);

    if (hBitmap)
    {
        SetClipboardData(CF_BITMAP, hBitmap);
    }

    CloseClipboard();
}

extern "C" UNITY_INTERFACE_EXPORT void* UNITY_INTERFACE_API GetClipboardImage(int* width, int* height)
{
    *width = 0;
    *height = 0;

    if (!OpenClipboard(NULL))
        return nullptr;

    HANDLE hBitmap = GetClipboardData(CF_BITMAP);
    if (!hBitmap)
    {
        CloseClipboard();
        return nullptr;
    }

    BITMAP bm;
    GetObject(hBitmap, sizeof(BITMAP), &bm);

    *width = bm.bmWidth;
    *height = bm.bmHeight;

    // �����ڴ�DC
    HDC hdcScreen = GetDC(NULL);
    HDC hdcMem = CreateCompatibleDC(hdcScreen);
    HBITMAP hOldBitmap = (HBITMAP)SelectObject(hdcMem, hBitmap);

    // �����ڴ沢������������
    int dataSize = bm.bmWidth * bm.bmHeight * 4;
    BYTE* pixelData = new BYTE[dataSize];

    BITMAPINFOHEADER bi = { 0 };
    bi.biSize = sizeof(BITMAPINFOHEADER);
    bi.biWidth = bm.bmWidth;
    bi.biHeight = -bm.bmHeight; // ��ֵ��ʾ���϶��µ�λͼ
    bi.biPlanes = 1;
    bi.biBitCount = 32;
    bi.biCompression = BI_RGB;

    GetDIBits(hdcMem, (HBITMAP)hBitmap, 0, bm.bmHeight, pixelData, (BITMAPINFO*)&bi, DIB_RGB_COLORS);

    // ����
    SelectObject(hdcMem, hOldBitmap);
    DeleteDC(hdcMem);
    ReleaseDC(NULL, hdcScreen);
    CloseClipboard();

    return pixelData;
}

// ��������������
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API ClearClipboard()
{
    if (OpenClipboard(NULL))
    {
        EmptyClipboard();
        CloseClipboard();
    }
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetHasClipboardText()
{
    if (!OpenClipboard(NULL))
        return false;

    bool result = IsClipboardFormatAvailable(CF_TEXT);
    CloseClipboard();
    return result;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetHasClipboardImage()
{
    if (!OpenClipboard(NULL))
        return false;

    bool result = IsClipboardFormatAvailable(CF_BITMAP);
    CloseClipboard();
    return result;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API IsFormatAvailable(unsigned int format)
{
    return IsClipboardFormatAvailable(format);
}

#pragma endregion

#pragma region Identity API

// �޸� Identity API �е� AllocateString ����
extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetUserNameNative()
{
    char username[256] = { 0 };
    DWORD size = sizeof(username);
    GetUserNameA(username, &size);
    return AllocateString(std::string(username));
}

extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetDomainNameNative()
{
    char domain[256] = { 0 };
    DWORD size = sizeof(domain);
    GetComputerNameExA(ComputerNameDnsDomain, domain, &size);
    return AllocateString(std::string(domain));
}

extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetMachineNameNative()
{
    char machineName[256] = { 0 };
    DWORD size = sizeof(machineName);
    GetComputerNameA(machineName, &size);
    return AllocateString(std::string(machineName));
}

extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetDeviceUniqueIdentifierNative()
{
    // ��ȡӲ����Ϣ��ΪΨһ��ʶ��
    HW_PROFILE_INFO hwProfileInfo;
    if (GetCurrentHwProfile(&hwProfileInfo))
    {
        return AllocateString(WideToUtf8(hwProfileInfo.szHwProfileGuid));
    }
    return AllocateString(std::string(""));
}

#pragma endregion

#pragma region Taskbar API

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetTaskbarProgressValue(int progressValue, int progressMax, int style)
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd)
        return;

    // �����������ӿ�
    ITaskbarList3* pTaskbarList = NULL;
    HRESULT hr = CoCreateInstance(CLSID_TaskbarList, NULL, CLSCTX_INPROC_SERVER, IID_ITaskbarList3, (void**)&pTaskbarList);
    if (SUCCEEDED(hr) && pTaskbarList)
    {
        pTaskbarList->HrInit();
        pTaskbarList->SetProgressValue(hwnd, progressValue, progressMax);
        // ʹ��Windowsԭ����TBPFLAG�������Զ���ö��
        pTaskbarList->SetProgressState(hwnd, (TBPFLAG)style);
        pTaskbarList->Release();
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetTaskbarProgressStyle(int style)
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd)
        return;

    ITaskbarList3* pTaskbarList = NULL;
    HRESULT hr = CoCreateInstance(CLSID_TaskbarList, NULL, CLSCTX_INPROC_SERVER, IID_ITaskbarList3, (void**)&pTaskbarList);
    if (SUCCEEDED(hr) && pTaskbarList)
    {
        pTaskbarList->HrInit();
        // ʹ��Windowsԭ����TBPFLAG�������Զ���ö��
        pTaskbarList->SetProgressState(hwnd, (TBPFLAG)style);
        pTaskbarList->Release();
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API HideTaskbarProgress()
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd)
        return;

    ITaskbarList3* pTaskbarList = NULL;
    HRESULT hr = CoCreateInstance(CLSID_TaskbarList, NULL, CLSCTX_INPROC_SERVER, IID_ITaskbarList3, (void**)&pTaskbarList);
    if (SUCCEEDED(hr) && pTaskbarList)
    {
        pTaskbarList->HrInit();
        // ʹ��Windowsԭ����TBPFLAG�������Զ���ö��
        pTaskbarList->SetProgressState(hwnd, TBPF_NOPROGRESS);
        pTaskbarList->Release();
    }
}

// ��������˸
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarOnce()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        FLASHWINFO fi = { 0 };
        fi.cbSize = sizeof(FLASHWINFO);
        fi.hwnd = hwnd;
        fi.dwFlags = FLASHW_ALL;
        fi.uCount = 1;
        fi.dwTimeout = 0;
        FlashWindowEx(&fi);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarUntilFocus()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        FLASHWINFO fi = { 0 };
        fi.cbSize = sizeof(FLASHWINFO);
        fi.hwnd = hwnd;
        fi.dwFlags = FLASHW_ALL | FLASHW_TIMERNOFG;
        fi.uCount = 0;
        fi.dwTimeout = 0;
        FlashWindowEx(&fi);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarStart()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        FLASHWINFO fi = { 0 };
        fi.cbSize = sizeof(FLASHWINFO);
        fi.hwnd = hwnd;
        fi.dwFlags = FLASHW_ALL | FLASHW_TIMER;
        fi.uCount = 0;
        fi.dwTimeout = 0;
        FlashWindowEx(&fi);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarStop()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        FLASHWINFO fi = { 0 };
        fi.cbSize = sizeof(FLASHWINFO);
        fi.hwnd = hwnd;
        fi.dwFlags = FLASHW_STOP;
        fi.uCount = 0;
        fi.dwTimeout = 0;
        FlashWindowEx(&fi);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarCount(int count)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        FLASHWINFO fi = { 0 };
        fi.cbSize = sizeof(FLASHWINFO);
        fi.hwnd = hwnd;
        fi.dwFlags = FLASHW_ALL;
        fi.uCount = count;
        fi.dwTimeout = 0;
        FlashWindowEx(&fi);
    }
}

#pragma endregion

#pragma region MessageBox API

extern "C" UNITY_INTERFACE_EXPORT int UNITY_INTERFACE_API ShowMessageBox(const char* message, const char* title, int buttons, int icon)
{
    return MessageBoxA(GetUnityWindowHandle(), message, title, buttons | icon);
}

#pragma endregion

#pragma region DragAndDrop API

// �ϷŻص�����
typedef void(*DropFilesCallback)(const char** files, int count);
static DropFilesCallback g_dropCallback = nullptr;

// �ϷŴ��ڹ���
LRESULT CALLBACK DragDropWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    switch (msg)
    {
    case WM_DROPFILES:
    {
        HDROP hDrop = (HDROP)wParam;
        UINT fileCount = DragQueryFile(hDrop, 0xFFFFFFFF, NULL, 0);

        if (fileCount > 0 && g_dropCallback)
        {
            std::vector<const char*> files;
            std::vector<std::string> fileStrings;

            for (UINT i = 0; i < fileCount; i++)
            {
                UINT pathLen = DragQueryFile(hDrop, i, NULL, 0) + 1;
                std::wstring filePath(pathLen, L'\0');
                DragQueryFile(hDrop, i, &filePath[0], pathLen);
                std::string filePathStr(filePath.begin(), filePath.end());
                fileStrings.push_back(filePathStr);
                files.push_back(fileStrings.back().c_str());
            }

            g_dropCallback(files.data(), files.size());
        }

        DragFinish(hDrop);
        return 0;
    }
    }

    return DefWindowProc(hwnd, msg, wParam, lParam);
}

// �ϷŴ��ھ��
static HWND g_dragDropHwnd = NULL;

// �����Ϸ�
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableDragAndDrop()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        DragAcceptFiles(hwnd, TRUE);
    }
}

// �����Ϸ�
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableDragAndDrop()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        DragAcceptFiles(hwnd, FALSE);
    }
}

// ע���ϷŻص�
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API RegisterDropCallback(void(*callback)(void* files, int count))
{
    g_dropCallback = (DropFilesCallback)callback;
}

#pragma endregion

#pragma region Cursor API

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetCursorPosition(int* x, int* y)
{
    POINT pt;
    if (GetCursorPos(&pt))
    {
        *x = pt.x;
        *y = pt.y;
    }
    else
    {
        *x = 0;
        *y = 0;
    }
}

// �� GetPhysicalCursorPosition ������
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetPhysicalCursorPosition(int* x, int* y)
{
    POINT pt;
    if (GetCursorPos(&pt))
    {
        // 检查是否支持GetDpiForMonitor API
        typedef HRESULT(WINAPI* GetDpiForMonitorFunc)(HMONITOR, int, UINT*, UINT*);
        HMODULE hShcore = LoadLibrary(L"Shcore.dll");
        if (hShcore)
        {
            GetDpiForMonitorFunc pGetDpiForMonitor =
                (GetDpiForMonitorFunc)GetProcAddress(hShcore, "GetDpiForMonitor");

            if (pGetDpiForMonitor)
            {
                HMONITOR hMonitor = MonitorFromPoint(pt, MONITOR_DEFAULTTONEAREST);
                UINT dpiX, dpiY;

                if (SUCCEEDED(pGetDpiForMonitor(hMonitor, 0, &dpiX, &dpiY))) // MDT_EFFECTIVE_DPI = 0
                {
                    *x = (int)(pt.x * (96.0 / dpiX));
                    *y = (int)(pt.y * (96.0 / dpiY));
                    FreeLibrary(hShcore);
                    return;
                }
            }
            FreeLibrary(hShcore);
        }

        // 如果DPI API不可用，直接返回物理坐标
        *x = pt.x;
        *y = pt.y;
    }
    else
    {
        *x = 0;
        *y = 0;
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetCursorPosition(int x, int y)
{
    SetCursorPos(x, y);
}

// �� SetPhysicalCursorPosition ������Ҳ��Ҫ�޸�
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetPhysicalCursorPosition(int x, int y)
{
    // 检查是否支持GetDpiForMonitor API
    typedef HRESULT(WINAPI* GetDpiForMonitorFunc)(HMONITOR, int, UINT*, UINT*);
    HMODULE hShcore = LoadLibrary(L"Shcore.dll");
    if (hShcore)
    {
        GetDpiForMonitorFunc pGetDpiForMonitor =
            (GetDpiForMonitorFunc)GetProcAddress(hShcore, "GetDpiForMonitor");

        if (pGetDpiForMonitor)
        {
            POINT pt;
            GetCursorPos(&pt);
            HMONITOR hMonitor = MonitorFromPoint(pt, MONITOR_DEFAULTTONEAREST);
            UINT dpiX, dpiY;

            if (SUCCEEDED(pGetDpiForMonitor(hMonitor, 0, &dpiX, &dpiY))) // MDT_EFFECTIVE_DPI = 0
            {
                int scaledX = (int)(x * (dpiX / 96.0));
                int scaledY = (int)(y * (dpiY / 96.0));
                SetCursorPos(scaledX, scaledY);
                FreeLibrary(hShcore);
                return;
            }
        }
        FreeLibrary(hShcore);
    }

    // 如果DPI API不可用，直接设置坐标
    SetCursorPos(x, y);
}

extern "C" UNITY_INTERFACE_EXPORT float UNITY_INTERFACE_API GetWindowOpacity()
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd)
        return 1.0f;

    BYTE alpha;
    DWORD flags;
    if (GetLayeredWindowAttributes(hwnd, NULL, &alpha, &flags))
    {
        return alpha / 255.0f;
    }
    return 1.0f;
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowOpacity(float opacity)
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd)
        return;

    // ȷ��opacity��0-1��Χ��
    opacity = max(0.0f, min(1.0f, opacity));

    // ת��Ϊ0-255��Χ
    BYTE alpha = (BYTE)(opacity * 255);

    // ȷ��������WS_EX_LAYERED��ʽ
    LONG_PTR exStyle = GetWindowLongPtr(hwnd, GWL_EXSTYLE);
    if (!(exStyle & WS_EX_LAYERED))
    {
        SetWindowLongPtr(hwnd, GWL_EXSTYLE, exStyle | WS_EX_LAYERED);
    }

    SetLayeredWindowAttributes(hwnd, 0, alpha, LWA_ALPHA);
}

// ���������������ַ��ַ���ת��Ϊ std::string
std::string WideToUtf8(const wchar_t* wstr)
{
    if (!wstr) return std::string();

    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, NULL, 0, NULL, NULL);
    if (size_needed <= 0) return std::string();

    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr, -1, &strTo[0], size_needed, NULL, NULL);
    strTo.resize(size_needed - 1);  // �Ƴ������ null ��ֹ��
    return strTo;
}


#pragma endregion