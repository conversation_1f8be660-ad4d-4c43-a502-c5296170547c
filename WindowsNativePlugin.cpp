#include "WindowsNativePlugin.h"
#include <Windows.h>
#include <ShlObj.h>
#include <string>
#include <vector>
#include <memory>

// 抑制已否决API的警告，因为我们需要兼容老系统
#pragma warning(push)
#pragma warning(disable: 4996)

// 条件包含头文件，避免在老系统上编译错误
#if (_WIN32_WINNT >= 0x0602) // Windows 8+
#include <ShellScalingApi.h>
#pragma comment(lib, "Shcore.lib")
#endif

#if (_WIN32_WINNT >= 0x0600) // Vista+
#include <dwmapi.h>
#pragma comment(lib, "dwmapi.lib")
#endif

// 全局变量：圆角半径和状态
static int g_cornerRadius = 0;
static bool g_isRoundedFrameless = false;

// 操作系统版本检测
static DWORD g_windowsVersion = 0;

// 获取Windows版本 - 使用更安全的方法
DWORD GetWindowsVersion()
{
    if (g_windowsVersion == 0)
    {
        // 使用 VerifyVersionInfo 和 VersionHelpers 的替代方法
        // 通过检测特定API的存在来判断Windows版本

        // 检查是否为Windows 8.1+（通过GetDpiForMonitor API）
        HMODULE hShcore = LoadLibrary(L"Shcore.dll");
        if (hShcore)
        {
            if (GetProcAddress(hShcore, "GetDpiForMonitor"))
            {
                g_windowsVersion = 0x0603; // Windows 8.1+
                FreeLibrary(hShcore);
                return g_windowsVersion;
            }
            FreeLibrary(hShcore);
        }

        // 检查是否为Windows 7+（通过ITaskbarList3）
        HMODULE hShell32 = LoadLibrary(L"shell32.dll");
        if (hShell32)
        {
            // 检查是否存在Windows 7的任务栏API
            typedef HRESULT(__stdcall *SHCreateItemFromParsingNameFunc)(PCWSTR, IBindCtx*, REFIID, void**);
            SHCreateItemFromParsingNameFunc pSHCreateItemFromParsingName =
                (SHCreateItemFromParsingNameFunc)GetProcAddress(hShell32, "SHCreateItemFromParsingName");

            if (pSHCreateItemFromParsingName)
            {
                g_windowsVersion = 0x0601; // Windows 7
                FreeLibrary(hShell32);
                return g_windowsVersion;
            }
            FreeLibrary(hShell32);
        }

        // 检查是否为Vista+（通过DWM API）
        HMODULE hDwmapi = LoadLibrary(L"dwmapi.dll");
        if (hDwmapi)
        {
            if (GetProcAddress(hDwmapi, "DwmExtendFrameIntoClientArea"))
            {
                g_windowsVersion = 0x0600; // Windows Vista
                FreeLibrary(hDwmapi);
                return g_windowsVersion;
            }
            FreeLibrary(hDwmapi);
        }

        // 检查是否支持分层窗口（Windows 2000+）
        HMODULE hUser32 = GetModuleHandle(L"user32.dll");
        if (hUser32 && GetProcAddress(hUser32, "SetLayeredWindowAttributes"))
        {
            g_windowsVersion = 0x0501; // Windows XP
        }
        else
        {
            g_windowsVersion = 0x0500; // Windows 2000 或更早
        }
    }
    return g_windowsVersion;
}

// 检查是否为Vista或更高版本
bool IsVistaOrLater()
{
    return GetWindowsVersion() >= 0x0600;
}

// 检查是否为Windows 7或更高版本
bool IsWin7OrLater()
{
    return GetWindowsVersion() >= 0x0601;
}

// 检查是否为Windows 8或更高版本
bool IsWin8OrLater()
{
    return GetWindowsVersion() >= 0x0602;
}

// 获取Unity窗口句柄
HWND GetUnityWindowHandle()
{
    return GetActiveWindow();
}

// 字符串辅助函数
char* AllocateString(const std::string& str)
{
    char* result = new char[str.length() + 1];
    strcpy_s(result, str.length() + 1, str.c_str());
    return result;
}

#pragma region Window API

// 窗口状态控制
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowFullscreen()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        MONITORINFO mi = { sizeof(mi) };
        if (GetMonitorInfo(MonitorFromWindow(hwnd, MONITOR_DEFAULTTOPRIMARY), &mi))
        {
            SetWindowLong(hwnd, GWL_STYLE, WS_POPUP | WS_VISIBLE);
            SetWindowPos(hwnd, HWND_TOP,
                mi.rcMonitor.left, mi.rcMonitor.top,
                mi.rcMonitor.right - mi.rcMonitor.left,
                mi.rcMonitor.bottom - mi.rcMonitor.top,
                SWP_FRAMECHANGED);
        }
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowMaximize()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        ShowWindow(hwnd, SW_MAXIMIZE);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowNormal()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        ShowWindow(hwnd, SW_NORMAL);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowMinimize()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        ShowWindow(hwnd, SW_MINIMIZE);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API RestoreWindow()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        ShowWindow(hwnd, SW_RESTORE);
    }
}

// 窗口样式控制
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFramed()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = WS_OVERLAPPEDWINDOW | WS_VISIBLE;
        SetWindowLong(hwnd, GWL_STYLE, style);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFramedWithoutTitleBar()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = WS_OVERLAPPED | WS_VISIBLE | WS_MINIMIZEBOX | WS_MAXIMIZEBOX | WS_SYSMENU;
        SetWindowLong(hwnd, GWL_STYLE, style);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFramedWithoutButtons()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = WS_OVERLAPPED | WS_VISIBLE | WS_BORDER | WS_CAPTION;
        SetWindowLong(hwnd, GWL_STYLE, style);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFrameless()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = WS_POPUP | WS_VISIBLE;
        SetWindowLong(hwnd, GWL_STYLE, style);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);

        // 重置圆角状态
        g_isRoundedFrameless = false;
    }
}

// 添加圆角无边框窗口支持
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleRoundedFrameless(int cornerRadius)
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd) return;

    // 保存圆角半径
    g_cornerRadius = cornerRadius;
    g_isRoundedFrameless = true;

    // 设置无边框窗口
    LONG style = WS_POPUP | WS_VISIBLE;
    SetWindowLong(hwnd, GWL_STYLE, style);

    // 设置扩展样式（仅在支持的系统上）
    if (IsVistaOrLater())
    {
        LONG exStyle = GetWindowLong(hwnd, GWL_EXSTYLE);
        exStyle |= WS_EX_LAYERED;
        SetWindowLong(hwnd, GWL_EXSTYLE, exStyle);

        // 尝试使用DWM API（Vista+）
        typedef HRESULT(WINAPI* DwmExtendFrameIntoClientAreaFunc)(HWND, const void*);
        typedef HRESULT(WINAPI* DwmSetWindowAttributeFunc)(HWND, DWORD, LPCVOID, DWORD);

        HMODULE hDwmapi = LoadLibrary(L"dwmapi.dll");
        if (hDwmapi)
        {
            DwmExtendFrameIntoClientAreaFunc pDwmExtendFrameIntoClientArea =
                (DwmExtendFrameIntoClientAreaFunc)GetProcAddress(hDwmapi, "DwmExtendFrameIntoClientArea");

            if (pDwmExtendFrameIntoClientArea)
            {
                // 应用DWM扩展
                int margins[4] = { 0, 0, 0, 0 };
                pDwmExtendFrameIntoClientArea(hwnd, margins);
            }

            // 检查是否支持Windows 11的圆角API
            DwmSetWindowAttributeFunc pDwmSetWindowAttribute =
                (DwmSetWindowAttributeFunc)GetProcAddress(hDwmapi, "DwmSetWindowAttribute");

            if (pDwmSetWindowAttribute)
            {
                // 尝试使用Windows 11的圆角API
                const DWORD DWMWA_WINDOW_CORNER_PREFERENCE = 33;
                const DWORD DWMWCP_ROUND = 2;
                DWORD preference = DWMWCP_ROUND;

                HRESULT hr = pDwmSetWindowAttribute(hwnd, DWMWA_WINDOW_CORNER_PREFERENCE, &preference, sizeof(preference));
                if (SUCCEEDED(hr))
                {
                    // Windows 11圆角API成功，直接返回
                    FreeLibrary(hDwmapi);
                    SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
                    return;
                }
            }

            FreeLibrary(hDwmapi);
        }
    }

    // 对于XP/Vista或API不可用的情况，使用传统的区域方法
    RECT rect;
    GetWindowRect(hwnd, &rect);
    int width = rect.right - rect.left;
    int height = rect.bottom - rect.top;

    // 创建圆角区域
    HRGN region = CreateRoundRectRgn(0, 0, width + 1, height + 1, cornerRadius, cornerRadius);
    SetWindowRgn(hwnd, region, TRUE);
    DeleteObject(region);

    // 刷新窗口
    SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
}

extern "C" UNITY_INTERFACE_EXPORT int UNITY_INTERFACE_API GetWindowCornerRadius()
{
    return g_cornerRadius;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleRoundedFrameless()
{
    return g_isRoundedFrameless;
}

// 窗口样式查询
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFramed()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_OVERLAPPEDWINDOW) == WS_OVERLAPPEDWINDOW;
    }
    return false;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFramedWithoutTitleBar()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_CAPTION) == 0 && (style & WS_OVERLAPPED) != 0;
    }
    return false;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFramedWithoutButtons()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_CAPTION) != 0 && (style & WS_MINIMIZEBOX) == 0 && (style & WS_MAXIMIZEBOX) == 0;
    }
    return false;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFrameless()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_POPUP) != 0 && (style & WS_OVERLAPPEDWINDOW) == 0;
    }
    return false;
}

// 窗口按钮控制
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableCloseButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        HMENU hMenu = GetSystemMenu(hwnd, FALSE);
        EnableMenuItem(hMenu, SC_CLOSE, MF_ENABLED);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableCloseButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        HMENU hMenu = GetSystemMenu(hwnd, FALSE);
        EnableMenuItem(hMenu, SC_CLOSE, MF_GRAYED);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableMaximizeButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        SetWindowLong(hwnd, GWL_STYLE, style | WS_MAXIMIZEBOX);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableMaximizeButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        SetWindowLong(hwnd, GWL_STYLE, style & ~WS_MAXIMIZEBOX);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableMinimizeButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        SetWindowLong(hwnd, GWL_STYLE, style | WS_MINIMIZEBOX);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableMinimizeButton()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        SetWindowLong(hwnd, GWL_STYLE, style & ~WS_MINIMIZEBOX);
        SetWindowPos(hwnd, NULL, 0, 0, 0, 0, SWP_FRAMECHANGED | SWP_NOMOVE | SWP_NOSIZE | SWP_NOZORDER);
    }
}

// 窗口按钮状态查询
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsCloseButtonEnabled()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        HMENU hMenu = GetSystemMenu(hwnd, FALSE);
        UINT state = GetMenuState(hMenu, SC_CLOSE, MF_BYCOMMAND);
        return (state & MF_GRAYED) == 0;
    }
    return false;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsMaximizeButtonEnabled()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_MAXIMIZEBOX) != 0;
    }
    return false;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsMinimizeButtonEnabled()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        return (style & WS_MINIMIZEBOX) != 0;
    }
    return false;
}

// 窗口位置和大小
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowPosition(int x, int y)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        SetWindowPos(hwnd, NULL, x, y, 0, 0, SWP_NOSIZE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowSize(int width, int height)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        SetWindowPos(hwnd, NULL, 0, 0, width, height, SWP_NOMOVE | SWP_NOZORDER);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetWindowPosition(int* x, int* y)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        RECT rect;
        GetWindowRect(hwnd, &rect);
        *x = rect.left;
        *y = rect.top;
    }
    else
    {
        *x = 0;
        *y = 0;
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetWindowSize(int* width, int* height)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        RECT rect;
        GetWindowRect(hwnd, &rect);
        *width = rect.right - rect.left;
        *height = rect.bottom - rect.top;
    }
    else
    {
        *width = 0;
        *height = 0;
    }
}
// 添加窗口拖动支持
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API StartWindowDrag()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        // 使用Windows原生的拖动消息
        ReleaseCapture();
        SendMessage(hwnd, WM_NCLBUTTONDOWN, HTCAPTION, 0);

        // 确保鼠标捕获被释放
        SetCapture(NULL);
    }
}
// 窗口标题
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowTitle(const char* title)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        SetWindowTextA(hwnd, title);
    }
}

extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetWindowTitle()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        int length = GetWindowTextLengthA(hwnd);
        if (length > 0)
        {
            std::string title(length + 1, '\0');
            GetWindowTextA(hwnd, &title[0], length + 1);
            return AllocateString(title);
        }
    }
    return AllocateString(std::string("")); // 修复：使用std::string包装空字符串
}

// 窗口透明度
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowAlpha(byte alpha)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        // 检查是否支持分层窗口（Windows 2000+，XP完全支持）
        typedef BOOL(WINAPI* SetLayeredWindowAttributesFunc)(HWND, COLORREF, BYTE, DWORD);
        HMODULE hUser32 = GetModuleHandle(L"user32.dll");
        if (hUser32)
        {
            SetLayeredWindowAttributesFunc pSetLayeredWindowAttributes =
                (SetLayeredWindowAttributesFunc)GetProcAddress(hUser32, "SetLayeredWindowAttributes");

            if (pSetLayeredWindowAttributes)
            {
                LONG_PTR exStyle = GetWindowLongPtr(hwnd, GWL_EXSTYLE);
                if (!(exStyle & WS_EX_LAYERED))
                {
                    SetWindowLongPtr(hwnd, GWL_EXSTYLE, exStyle | WS_EX_LAYERED);
                }
                pSetLayeredWindowAttributes(hwnd, 0, alpha, 2); // LWA_ALPHA = 2
            }
        }
    }
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetWindowHasAlpha()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        LONG_PTR exStyle = GetWindowLongPtr(hwnd, GWL_EXSTYLE);
        return (exStyle & WS_EX_LAYERED) != 0;
    }
    return false;
}

#pragma endregion

#pragma region Clipboard API

// 剪贴板文本操作
// 修复 SetClipboardText 函数中的 size_t 到 int 转换警告
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetClipboardText(const char* text, bool emptyClipboardBeforeSet)
{
    if (!OpenClipboard(NULL))
        return;

    if (emptyClipboardBeforeSet)
        EmptyClipboard();

    size_t len = strlen(text) + 1;
    // 使用 SIZE_T 类型或显式转换，避免警告
    HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, static_cast<SIZE_T>(len));
    if (hMem)
    {
        char* pMem = static_cast<char*>(GlobalLock(hMem));
        if (pMem)
        {
            memcpy(pMem, text, len);
            GlobalUnlock(hMem);
            SetClipboardData(CF_TEXT, hMem);
        }
    }

    CloseClipboard();
}

extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetClipboardText()
{
    if (!OpenClipboard(NULL))
        return AllocateString(std::string("")); // 修复：使用std::string包装空字符串

    HANDLE hData = GetClipboardData(CF_TEXT);
    if (!hData)
    {
        CloseClipboard();
        return AllocateString(std::string("")); // 修复：使用std::string包装空字符串
    }

    char* pText = (char*)GlobalLock(hData);
    if (!pText)
    {
        CloseClipboard();
        return AllocateString(std::string("")); // 修复：使用std::string包装空字符串
    }

    const char* result = AllocateString(std::string(pText)); // 修复：使用std::string包装
    GlobalUnlock(hData);
    CloseClipboard();
    return result;
}

// 剪贴板图像操作
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetClipboardImage(void* texturePtr, int width, int height, bool emptyClipboardBeforeSet)
{
    if (!OpenClipboard(NULL))
        return;

    if (emptyClipboardBeforeSet)
        EmptyClipboard();

    // 创建DIB位图
    BITMAPINFOHEADER bi = { 0 };
    bi.biSize = sizeof(BITMAPINFOHEADER);
    bi.biWidth = width;
    bi.biHeight = -height; // 负值表示自上而下的位图
    bi.biPlanes = 1;
    bi.biBitCount = 32;
    bi.biCompression = BI_RGB;

    HDC hdc = GetDC(NULL);
    HBITMAP hBitmap = CreateDIBitmap(hdc, &bi, CBM_INIT, texturePtr, (BITMAPINFO*)&bi, DIB_RGB_COLORS);
    ReleaseDC(NULL, hdc);

    if (hBitmap)
    {
        SetClipboardData(CF_BITMAP, hBitmap);
    }

    CloseClipboard();
}

extern "C" UNITY_INTERFACE_EXPORT void* UNITY_INTERFACE_API GetClipboardImage(int* width, int* height)
{
    *width = 0;
    *height = 0;

    if (!OpenClipboard(NULL))
        return nullptr;

    HANDLE hBitmap = GetClipboardData(CF_BITMAP);
    if (!hBitmap)
    {
        CloseClipboard();
        return nullptr;
    }

    BITMAP bm;
    GetObject(hBitmap, sizeof(BITMAP), &bm);

    *width = bm.bmWidth;
    *height = bm.bmHeight;

    // 创建内存DC
    HDC hdcScreen = GetDC(NULL);
    HDC hdcMem = CreateCompatibleDC(hdcScreen);
    HBITMAP hOldBitmap = (HBITMAP)SelectObject(hdcMem, hBitmap);

    // 分配内存并复制像素数据
    int dataSize = bm.bmWidth * bm.bmHeight * 4;
    BYTE* pixelData = new BYTE[dataSize];

    BITMAPINFOHEADER bi = { 0 };
    bi.biSize = sizeof(BITMAPINFOHEADER);
    bi.biWidth = bm.bmWidth;
    bi.biHeight = -bm.bmHeight; // 负值表示自上而下的位图
    bi.biPlanes = 1;
    bi.biBitCount = 32;
    bi.biCompression = BI_RGB;

    GetDIBits(hdcMem, (HBITMAP)hBitmap, 0, bm.bmHeight, pixelData, (BITMAPINFO*)&bi, DIB_RGB_COLORS);

    // 清理
    SelectObject(hdcMem, hOldBitmap);
    DeleteDC(hdcMem);
    ReleaseDC(NULL, hdcScreen);
    CloseClipboard();

    return pixelData;
}

// 剪贴板其他操作
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API ClearClipboard()
{
    if (OpenClipboard(NULL))
    {
        EmptyClipboard();
        CloseClipboard();
    }
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetHasClipboardText()
{
    if (!OpenClipboard(NULL))
        return false;

    bool result = IsClipboardFormatAvailable(CF_TEXT);
    CloseClipboard();
    return result;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetHasClipboardImage()
{
    if (!OpenClipboard(NULL))
        return false;

    bool result = IsClipboardFormatAvailable(CF_BITMAP);
    CloseClipboard();
    return result;
}

extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API IsFormatAvailable(unsigned int format)
{
    return IsClipboardFormatAvailable(format);
}

#pragma endregion

#pragma region Identity API

// 修复 Identity API 中的 AllocateString 调用
extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetUserNameNative()
{
    char username[256] = { 0 };
    DWORD size = sizeof(username);
    GetUserNameA(username, &size);
    return AllocateString(std::string(username));
}

extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetDomainNameNative()
{
    char domain[256] = { 0 };
    DWORD size = sizeof(domain);
    GetComputerNameExA(ComputerNameDnsDomain, domain, &size);
    return AllocateString(std::string(domain));
}

extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetMachineNameNative()
{
    char machineName[256] = { 0 };
    DWORD size = sizeof(machineName);
    GetComputerNameA(machineName, &size);
    return AllocateString(std::string(machineName));
}

extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetDeviceUniqueIdentifierNative()
{
    // 获取硬件信息作为唯一标识符
    HW_PROFILE_INFO hwProfileInfo;
    if (GetCurrentHwProfile(&hwProfileInfo))
    {
        return AllocateString(WideToUtf8(hwProfileInfo.szHwProfileGuid));
    }
    return AllocateString(std::string(""));
}

#pragma endregion

#pragma region Taskbar API

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetTaskbarProgressValue(int progressValue, int progressMax, int style)
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd)
        return;

    // 任务栏进度条仅在Windows 7+支持
    if (!IsWin7OrLater())
        return;

    // 初始化COM
    CoInitialize(NULL);

    // 创建任务栏接口
    typedef interface ITaskbarList3 ITaskbarList3;
    ITaskbarList3* pTaskbarList = NULL;

    // 手动定义CLSID和IID以避免依赖问题
    const CLSID CLSID_TaskbarList_Manual = { 0x56FDF344, 0xFD6D, 0x11d0, {0x95, 0x8A, 0x00, 0x60, 0x97, 0xC9, 0xA0, 0x90} };
    const IID IID_ITaskbarList3_Manual = { 0xea1afb91, 0x9e28, 0x4b86, {0x90, 0xe9, 0x9e, 0x9f, 0x8a, 0x5e, 0xef, 0xaf} };

    HRESULT hr = CoCreateInstance(CLSID_TaskbarList_Manual, NULL, CLSCTX_INPROC_SERVER, IID_ITaskbarList3_Manual, (void**)&pTaskbarList);
    if (SUCCEEDED(hr) && pTaskbarList)
    {
        // 使用函数指针调用方法以避免编译时依赖
        typedef HRESULT(__stdcall* HrInitFunc)(ITaskbarList3*);
        typedef HRESULT(__stdcall* SetProgressValueFunc)(ITaskbarList3*, HWND, ULONGLONG, ULONGLONG);
        typedef HRESULT(__stdcall* SetProgressStateFunc)(ITaskbarList3*, HWND, int);
        typedef ULONG(__stdcall* ReleaseFunc)(ITaskbarList3*);

        // 获取虚函数表
        void** vtable = *(void***)pTaskbarList;
        HrInitFunc pHrInit = (HrInitFunc)vtable[3];
        SetProgressValueFunc pSetProgressValue = (SetProgressValueFunc)vtable[9];
        SetProgressStateFunc pSetProgressState = (SetProgressStateFunc)vtable[10];
        ReleaseFunc pRelease = (ReleaseFunc)vtable[2];

        if (pHrInit && pSetProgressValue && pSetProgressState && pRelease)
        {
            pHrInit(pTaskbarList);
            pSetProgressValue(pTaskbarList, hwnd, progressValue, progressMax);
            pSetProgressState(pTaskbarList, hwnd, style);
            pRelease(pTaskbarList);
        }
    }

    CoUninitialize();
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetTaskbarProgressStyle(int style)
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd || !IsWin7OrLater())
        return;

    // 简化的任务栏样式设置，使用与上面相同的方法
    SetTaskbarProgressValue(0, 100, style);
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API HideTaskbarProgress()
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd || !IsWin7OrLater())
        return;

    // 隐藏任务栏进度，使用样式0（TBPF_NOPROGRESS）
    SetTaskbarProgressValue(0, 100, 0);
}

// 任务栏闪烁
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarOnce()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        FLASHWINFO fi = { 0 };
        fi.cbSize = sizeof(FLASHWINFO);
        fi.hwnd = hwnd;
        fi.dwFlags = FLASHW_ALL;
        fi.uCount = 1;
        fi.dwTimeout = 0;
        FlashWindowEx(&fi);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarUntilFocus()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        FLASHWINFO fi = { 0 };
        fi.cbSize = sizeof(FLASHWINFO);
        fi.hwnd = hwnd;
        fi.dwFlags = FLASHW_ALL | FLASHW_TIMERNOFG;
        fi.uCount = 0;
        fi.dwTimeout = 0;
        FlashWindowEx(&fi);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarStart()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        FLASHWINFO fi = { 0 };
        fi.cbSize = sizeof(FLASHWINFO);
        fi.hwnd = hwnd;
        fi.dwFlags = FLASHW_ALL | FLASHW_TIMER;
        fi.uCount = 0;
        fi.dwTimeout = 0;
        FlashWindowEx(&fi);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarStop()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        FLASHWINFO fi = { 0 };
        fi.cbSize = sizeof(FLASHWINFO);
        fi.hwnd = hwnd;
        fi.dwFlags = FLASHW_STOP;
        fi.uCount = 0;
        fi.dwTimeout = 0;
        FlashWindowEx(&fi);
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarCount(int count)
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        FLASHWINFO fi = { 0 };
        fi.cbSize = sizeof(FLASHWINFO);
        fi.hwnd = hwnd;
        fi.dwFlags = FLASHW_ALL;
        fi.uCount = count;
        fi.dwTimeout = 0;
        FlashWindowEx(&fi);
    }
}

#pragma endregion

#pragma region MessageBox API

extern "C" UNITY_INTERFACE_EXPORT int UNITY_INTERFACE_API ShowMessageBox(const char* message, const char* title, int buttons, int icon)
{
    return MessageBoxA(GetUnityWindowHandle(), message, title, buttons | icon);
}

#pragma endregion

#pragma region DragAndDrop API

// 拖放回调函数
typedef void(*DropFilesCallback)(const char** files, int count);
static DropFilesCallback g_dropCallback = nullptr;

// 拖放窗口过程
LRESULT CALLBACK DragDropWndProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    switch (msg)
    {
    case WM_DROPFILES:
    {
        HDROP hDrop = (HDROP)wParam;
        UINT fileCount = DragQueryFile(hDrop, 0xFFFFFFFF, NULL, 0);

        if (fileCount > 0 && g_dropCallback)
        {
            std::vector<const char*> files;
            std::vector<std::string> fileStrings;

            for (UINT i = 0; i < fileCount; i++)
            {
                UINT pathLen = DragQueryFile(hDrop, i, NULL, 0) + 1;
                std::wstring filePath(pathLen, L'\0');
                DragQueryFile(hDrop, i, &filePath[0], pathLen);
                std::string filePathStr(filePath.begin(), filePath.end());
                fileStrings.push_back(filePathStr);
                files.push_back(fileStrings.back().c_str());
            }

            g_dropCallback(files.data(), files.size());
        }

        DragFinish(hDrop);
        return 0;
    }
    }

    return DefWindowProc(hwnd, msg, wParam, lParam);
}

// 拖放窗口句柄
static HWND g_dragDropHwnd = NULL;

// 启用拖放
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableDragAndDrop()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        DragAcceptFiles(hwnd, TRUE);
    }
}

// 禁用拖放
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableDragAndDrop()
{
    HWND hwnd = GetUnityWindowHandle();
    if (hwnd)
    {
        DragAcceptFiles(hwnd, FALSE);
    }
}

// 注册拖放回调
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API RegisterDropCallback(void(*callback)(void* files, int count))
{
    g_dropCallback = (DropFilesCallback)callback;
}

#pragma endregion

#pragma region Cursor API

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetCursorPosition(int* x, int* y)
{
    POINT pt;
    if (GetCursorPos(&pt))
    {
        *x = pt.x;
        *y = pt.y;
    }
    else
    {
        *x = 0;
        *y = 0;
    }
}

// 获取物理鼠标位置（考虑DPI缩放）
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetPhysicalCursorPosition(int* x, int* y)
{
    POINT pt;
    if (GetCursorPos(&pt))
    {
        // 仅在Windows 8.1+尝试使用DPI API
        if (IsWin8OrLater())
        {
            typedef HRESULT(WINAPI* GetDpiForMonitorFunc)(HMONITOR, int, UINT*, UINT*);
            HMODULE hShcore = LoadLibrary(L"Shcore.dll");
            if (hShcore)
            {
                GetDpiForMonitorFunc pGetDpiForMonitor =
                    (GetDpiForMonitorFunc)GetProcAddress(hShcore, "GetDpiForMonitor");

                if (pGetDpiForMonitor)
                {
                    HMONITOR hMonitor = MonitorFromPoint(pt, MONITOR_DEFAULTTONEAREST);
                    UINT dpiX, dpiY;

                    if (SUCCEEDED(pGetDpiForMonitor(hMonitor, 0, &dpiX, &dpiY))) // MDT_EFFECTIVE_DPI = 0
                    {
                        *x = (int)(pt.x * (96.0 / dpiX));
                        *y = (int)(pt.y * (96.0 / dpiY));
                        FreeLibrary(hShcore);
                        return;
                    }
                }
                FreeLibrary(hShcore);
            }
        }

        // 对于XP/Vista/Win7，或者DPI API不可用的情况，直接返回坐标
        // 在这些系统上，DPI缩放通常不是问题
        *x = pt.x;
        *y = pt.y;
    }
    else
    {
        *x = 0;
        *y = 0;
    }
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetCursorPosition(int x, int y)
{
    SetCursorPos(x, y);
}

// 设置物理鼠标位置（考虑DPI缩放）
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetPhysicalCursorPosition(int x, int y)
{
    // 仅在Windows 8.1+尝试使用DPI API
    if (IsWin8OrLater())
    {
        typedef HRESULT(WINAPI* GetDpiForMonitorFunc)(HMONITOR, int, UINT*, UINT*);
        HMODULE hShcore = LoadLibrary(L"Shcore.dll");
        if (hShcore)
        {
            GetDpiForMonitorFunc pGetDpiForMonitor =
                (GetDpiForMonitorFunc)GetProcAddress(hShcore, "GetDpiForMonitor");

            if (pGetDpiForMonitor)
            {
                POINT pt;
                GetCursorPos(&pt);
                HMONITOR hMonitor = MonitorFromPoint(pt, MONITOR_DEFAULTTONEAREST);
                UINT dpiX, dpiY;

                if (SUCCEEDED(pGetDpiForMonitor(hMonitor, 0, &dpiX, &dpiY))) // MDT_EFFECTIVE_DPI = 0
                {
                    int scaledX = (int)(x * (dpiX / 96.0));
                    int scaledY = (int)(y * (dpiY / 96.0));
                    SetCursorPos(scaledX, scaledY);
                    FreeLibrary(hShcore);
                    return;
                }
            }
            FreeLibrary(hShcore);
        }
    }

    // 对于XP/Vista/Win7，或者DPI API不可用的情况，直接设置坐标
    SetCursorPos(x, y);
}

extern "C" UNITY_INTERFACE_EXPORT float UNITY_INTERFACE_API GetWindowOpacity()
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd)
        return 1.0f;

    // 检查是否支持分层窗口属性获取
    typedef BOOL(WINAPI* GetLayeredWindowAttributesFunc)(HWND, COLORREF*, BYTE*, DWORD*);
    HMODULE hUser32 = GetModuleHandle(L"user32.dll");
    if (hUser32)
    {
        GetLayeredWindowAttributesFunc pGetLayeredWindowAttributes =
            (GetLayeredWindowAttributesFunc)GetProcAddress(hUser32, "GetLayeredWindowAttributes");

        if (pGetLayeredWindowAttributes)
        {
            BYTE alpha;
            DWORD flags;
            if (pGetLayeredWindowAttributes(hwnd, NULL, &alpha, &flags))
            {
                return alpha / 255.0f;
            }
        }
    }
    return 1.0f;
}

extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowOpacity(float opacity)
{
    HWND hwnd = GetUnityWindowHandle();
    if (!hwnd)
        return;

    // 确保opacity在0-1范围内
    opacity = max(0.0f, min(1.0f, opacity));

    // 转换为0-255范围
    BYTE alpha = (BYTE)(opacity * 255);

    // 检查是否支持分层窗口
    typedef BOOL(WINAPI* SetLayeredWindowAttributesFunc)(HWND, COLORREF, BYTE, DWORD);
    HMODULE hUser32 = GetModuleHandle(L"user32.dll");
    if (hUser32)
    {
        SetLayeredWindowAttributesFunc pSetLayeredWindowAttributes =
            (SetLayeredWindowAttributesFunc)GetProcAddress(hUser32, "SetLayeredWindowAttributes");

        if (pSetLayeredWindowAttributes)
        {
            // 确保窗口有WS_EX_LAYERED样式
            LONG_PTR exStyle = GetWindowLongPtr(hwnd, GWL_EXSTYLE);
            if (!(exStyle & WS_EX_LAYERED))
            {
                SetWindowLongPtr(hwnd, GWL_EXSTYLE, exStyle | WS_EX_LAYERED);
            }

            pSetLayeredWindowAttributes(hwnd, 0, alpha, 2); // LWA_ALPHA = 2
        }
    }
}

// 辅助函数：将宽字符字符串转换为 std::string
std::string WideToUtf8(const wchar_t* wstr)
{
    if (!wstr) return std::string();

    int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr, -1, NULL, 0, NULL, NULL);
    if (size_needed <= 0) return std::string();

    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr, -1, &strTo[0], size_needed, NULL, NULL);
    strTo.resize(size_needed - 1);  // 移除多余的 null 终止符
    return strTo;
}


#pragma endregion

// 恢复警告设置
#pragma warning(pop)