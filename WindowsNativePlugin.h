#pragma once

#include <Windows.h>
#include <string> // 添加 string 头文件

// 条件包含，避免在老系统上编译错误
#if (_WIN32_WINNT >= 0x0600) // Vista+
#include <shobjidl.h>
#endif

// ����Unity�ӿں�
#define UNITY_INTERFACE_API __stdcall
#define UNITY_INTERFACE_EXPORT __declspec(dllexport)

// 任务栏进度状态 - 避免与Windows API冲突，重命名为自定义枚举
enum TBPFLAG_CUSTOM
{
    TBPF_NOPROGRESS_CUSTOM = 0,
    TBPF_INDETERMINATE_CUSTOM = 0x1,
    TBPF_NORMAL_CUSTOM = 0x2,
    TBPF_ERROR_CUSTOM = 0x4,
    TBPF_PAUSED_CUSTOM = 0x8
};

// 获取Unity窗口句柄
HWND GetUnityWindowHandle();

// 字符串辅助函数 - 修复声明
char* AllocateString(const std::string& str);

// 宽字符转UTF8字符串辅助函数
std::string WideToUtf8(const wchar_t* wstr);

// 窗口API
// 添加窗口拖动API
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API StartWindowDrag();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowFullscreen();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowMaximize();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowNormal();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowMinimize();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API RestoreWindow();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFramed();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFramedWithoutTitleBar();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFramedWithoutButtons();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleFrameless();
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFramed();
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFramedWithoutTitleBar();
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFramedWithoutButtons();
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleFrameless();

// 添加圆角无边框窗口API
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowStyleRoundedFrameless(int cornerRadius);
extern "C" UNITY_INTERFACE_EXPORT int UNITY_INTERFACE_API GetWindowCornerRadius();
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsWindowStyleRoundedFrameless();

// 窗口按钮控制
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableCloseButton();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableCloseButton();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableMaximizeButton();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableMaximizeButton();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableMinimizeButton();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableMinimizeButton();
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsCloseButtonEnabled();
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsMaximizeButtonEnabled();
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetIsMinimizeButtonEnabled();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowPosition(int x, int y);
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowSize(int width, int height);
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetWindowPosition(int* x, int* y);
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetWindowSize(int* width, int* height);
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowTitle(const char* title);
extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetWindowTitle();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowAlpha(byte alpha);
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetWindowHasAlpha();

// 剪贴板API
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetClipboardText(const char* text, bool emptyClipboardBeforeSet);
extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetClipboardText();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetClipboardImage(void* texturePtr, int width, int height, bool emptyClipboardBeforeSet);
extern "C" UNITY_INTERFACE_EXPORT void* UNITY_INTERFACE_API GetClipboardImage(int* width, int* height);
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API ClearClipboard();
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetHasClipboardText();
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API GetHasClipboardImage();
extern "C" UNITY_INTERFACE_EXPORT bool UNITY_INTERFACE_API IsFormatAvailable(unsigned int format);

// 身份API
extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetUserNameNative();
extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetDomainNameNative();
extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetMachineNameNative();
extern "C" UNITY_INTERFACE_EXPORT const char* UNITY_INTERFACE_API GetDeviceUniqueIdentifierNative();

// 任务栏API
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetTaskbarProgressValue(int progressValue, int progressMax, int style);
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetTaskbarProgressStyle(int style);
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API HideTaskbarProgress();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarOnce();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarUntilFocus();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarStart();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarStop();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API FlashTaskbarCount(int count);

// 消息框API
extern "C" UNITY_INTERFACE_EXPORT int UNITY_INTERFACE_API ShowMessageBox(const char* message, const char* title, int buttons, int icon);

// 拖放API
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API EnableDragAndDrop();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API DisableDragAndDrop();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API RegisterDropCallback(void(*callback)(void* files, int count));

// 光标API
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetCursorPosition(int* x, int* y);
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API GetPhysicalCursorPosition(int* x, int* y);
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetCursorPosition(int x, int y);
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetPhysicalCursorPosition(int x, int y);
// 窗口API
extern "C" UNITY_INTERFACE_EXPORT float UNITY_INTERFACE_API GetWindowOpacity();
extern "C" UNITY_INTERFACE_EXPORT void UNITY_INTERFACE_API SetWindowOpacity(float opacity);